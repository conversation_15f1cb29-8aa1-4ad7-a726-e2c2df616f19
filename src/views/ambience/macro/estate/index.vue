<template>
  <CommonTabs :hideList="[2]" title="产业数据">
    <template #searchArea>
      <el-form :inline="true" :model="formInline" class="demo-form-inline tabs-form">
        <el-form-item style="width: 300px">
          <el-date-picker v-model="formInline.date" type="monthrange" start-placeholder="开始日期" end-placeholder="结束日期"
            :disabled-date="disabledDate" value-format="YYYY-MM" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSubmit" style="margin-top: 2px" :loading="estateLoading">
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </template>
    <!-- <div class="el-row-sclorl"> -->
    <el-row :gutter="15" v-loading="estateLoading" style="margin-left: 0;margin-right: 0;">
      <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8" v-for="(item, index) in estateChartList" :key="index">
        <Chart :titleIcon="`data${(index % 6) + 1}`" v-bind="{ ...item }" />
      </el-col>
    </el-row>
    <!-- </div> -->
  </CommonTabs>
</template>

<script setup>
import Chart from '@/views/ambience/components/CommonChart/Chart'

import CommonTabs from '@/views/components/tabs/CommonTabs'
let store = useStore()

const formInline = computed(() => store.state.macro.estateParams)
const estateChartList = computed(() => store.state.macro.estateChartList)
const estateLoading = computed(() => store.state.macro.estateLoading)

onMounted(() => {
  store.dispatch('macro/getMacroEnvIndData')
})

const disabledDate = time => {
  return time.getTime() > Date.now()
}

const onSubmit = () => {
  const data = formInline?.value
  store.dispatch('macro/getMacroEnvIndData', data)
}
</script>

<style lang="scss" scoped>
@import '@/views/ambience/components/CommonBox/common.scss';
@import '@/assets/styles/bi/variables.module.scss';

.transportRoot {
  border: 1px solid $border-btn-color;
  margin-top: 10px;
  // padding-top: 10px;
}

// .el-row-sclorl{
//   height:calc($bi-main-height - 160px);
//   overflow-x:hidden;
// }
// .el-col{
//   margin-right:16px;
// }

::v-deep .el-row .el-col:nth-child(3n) {
  padding-right: 0px !important;
}
</style>
