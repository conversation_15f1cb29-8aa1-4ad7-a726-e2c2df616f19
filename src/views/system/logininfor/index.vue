<template>
  <div class="table-list">
    <el-form :model="queryParams" ref="queryRef" label-width="0" class="table-list__search">
      <el-row :gutter="16">
        <el-col :span="3">
          <el-form-item prop="ipaddr">
            <el-input
              v-model="queryParams.ipaddr"
              placeholder="登录地址"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="3"
          ><el-form-item prop="userName">
            <el-input
              v-model="queryParams.userName"
              placeholder="员工号"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-form-item prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="登录状态"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in sys_common_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item>
            <el-date-picker
              v-model="dateRange"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="登录时间从"
              end-placeholder="登录时间至"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div
      v-if="proxy.hasPermission(['monitor:logininfor:remove', ['monitor:logininfor:export']]) > 0"
      class="table-list__control"
    >
      <el-button
        type="danger"
        plain
        icon="Delete"
        :disabled="multiple"
        @click="handleDelete"
        v-hasPermi="['monitor:logininfor:remove']"
        >删除</el-button
      >
      <el-button
        type="danger"
        plain
        icon="Delete"
        @click="handleClean"
        v-hasPermi="['monitor:logininfor:remove']"
        >清空</el-button
      >
      <el-button
        type="warning"
        plain
        icon="Download"
        @click="handleExport"
        v-hasPermi="['monitor:logininfor:export']"
        >导出</el-button
      >
    </div>

    <el-table
      ref="logininforRef"
      v-loading="loading"
      :data="logininforList"
      @selection-change="handleSelectionChange"
      :default-sort="defaultSort"
      @sort-change="handleSortChange"
      height="100%"
      class="table-list__content"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="访问编号" prop="infoId" />
      <el-table-column
        label="员工号"
        prop="userName"
        :show-overflow-tooltip="true"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
      />
      <el-table-column label="登录状态" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_common_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="描述" prop="msg" />
      <el-table-column
        label="访问时间"
        prop="accessTime"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.accessTime) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <BiPagination
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Logininfor">
import BiPagination from '@/views/components/BiPagination.vue'
import { list, delLogininfor, cleanLogininfor } from '@/api/system/logininfor'

const { proxy } = getCurrentInstance()
const { sys_common_status } = proxy.useDict('sys_common_status')

const logininforList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const multiple = ref(true)
const total = ref(0)
const dateRange = ref([])
const defaultSort = ref({ prop: 'loginTime', order: 'descending' })

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 15,
  ipaddr: undefined,
  userName: undefined,
  status: undefined,
  orderByColumn: undefined,
  isAsc: undefined
})

/** 查询登录日志列表 */
function getList() {
  loading.value = true
  list(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    logininforList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}
/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm('queryRef')
  proxy.$refs['logininforRef'].sort(defaultSort.value.prop, defaultSort.value.order)
  handleQuery()
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.infoId)
  multiple.value = !selection.length
}
/** 排序触发事件 */
function handleSortChange(column, prop, order) {
  queryParams.value.orderByColumn = column.prop
  queryParams.value.isAsc = column.order
  getList()
}
/** 删除按钮操作 */
function handleDelete(row) {
  const infoIds = row.infoId || ids.value
  proxy.$modal
    .confirm('是否确认删除访问编号为"' + infoIds + '"的数据项?')
    .then(function () {
      return delLogininfor(infoIds)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess('删除成功')
    })
    .catch(() => {})
}
/** 清空按钮操作 */
function handleClean() {
  proxy.$modal
    .confirm('是否确认清空所有登录日志数据项?')
    .then(function () {
      return cleanLogininfor()
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess('清空成功')
    })
    .catch(() => {})
}
/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'system/logininfor/export',
    {
      ...queryParams.value
    },
    `config_${new Date().getTime()}.xlsx`
  )
}

getList()
</script>
